#!/usr/bin/env python3

from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
from pydub import AudioSegment
import logging
import os
import json
import cv2
import skvideo.io

app = Flask(__name__)
CORS(app, 
     origins="*", 
     allow_headers=["Content-Type", "Authorization", "X-Requested-With"], 
     methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
     supports_credentials=False)

logging.basicConfig(
    level=logging.INFO,
    filename="logger.log",
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

LED_FANS_WIDTH = 500
LED_FANS_HEIGHT = 500
LED_FANS_FPS = 15

STORAGE_CONFIG = {
    "base_dir": "led-fans-storage",
    "directories": {
        "materials": {
            "image": "materials/images",
            "video": "materials/videos",
            "audio": "materials/music"
        },
        "medias": "medias",
        "metadata": "metadata"
    },
    "indexes": {
        "materials": {
            "image": 0,
            "video": 0,
            "audio": 0
        },
        "medias": 0,
    },
    "preview_slots": {
        "music": [{"music_file_path": ""} for _ in range(10)],
        "media": [{"video_file_path": "", "music_file_path": ""} for _ in range(10)]
    }
}

def initialize():
    global STORAGE_CONFIG
    dirs = [
        STORAGE_CONFIG["directories"]["materials"]["image"],
        STORAGE_CONFIG["directories"]["materials"]["video"],
        STORAGE_CONFIG["directories"]["materials"]["audio"],
        STORAGE_CONFIG["directories"]["medias"],
        STORAGE_CONFIG["directories"]["metadata"]
    ]
    
    for dir_path in dirs:
        full_path = os.path.join(STORAGE_CONFIG["base_dir"], dir_path)
        try:
            os.makedirs(full_path)
            logger.info(f"Created directory: {full_path}")
        except Exception as error:
            pass

    storage_json_file_path = os.path.join(STORAGE_CONFIG["base_dir"], STORAGE_CONFIG["directories"]["metadata"], "storage.json")
    if os.path.exists(storage_json_file_path):
        with open(storage_json_file_path, "r") as f:
            STORAGE_CONFIG = json.load(f)
    else:
        with open(storage_json_file_path, "w") as f:
            json.dump(STORAGE_CONFIG, f)
    
def get_image_properties(image_path):
    img = cv2.imread(image_path)
    if img is None:
        logger.error(f"Could not read image file: {image_path}")
        return None
    height, width, _ = img.shape

    properties = {
        "width": width,
        "height": height,
    }
    return properties

def get_video_properties(video_path):
    cap = cv2.VideoCapture(video_path)

    if not cap.isOpened():
        logger.info(f"Error: Could not open video file {video_path}")
        return None

    properties = {
        "width": int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
        "height": int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
        "fps": cap.get(cv2.CAP_PROP_FPS),
        "frame_count": int(cap.get(cv2.CAP_PROP_FRAME_COUNT)),
        "codec": int(cap.get(cv2.CAP_PROP_FOURCC)),
        "duration_seconds": cap.get(cv2.CAP_PROP_FRAME_COUNT) / cap.get(cv2.CAP_PROP_FPS) if cap.get(cv2.CAP_PROP_FPS) > 0 else 0
    }

    cap.release()
    return properties

def get_audio_properties(audio_path):
    try:
        audio = AudioSegment.from_file(audio_path)
        properties = {
            "channels": audio.channels,
            "sample_rate": audio.frame_rate,
            "bit_depth": audio.sample_width * 8,
            "duration_seconds": len(audio) / 1000.0
        }
        return properties
    except Exception as e:
        logger.info(f"Error getting audio properties: {e}")
        return None

def resize_video_by_dimension(input_video_path, output_video_path, target_width, target_height, start_time, end_time, target_fps):
    cap = cv2.VideoCapture(input_video_path)

    if not cap.isOpened():
        logger.info(f"Error: Could not open video file {input_video_path}")
        return False

    original_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    original_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = cap.get(cv2.CAP_PROP_FPS)
    fourcc = cv2.VideoWriter_fourcc(*"avc1")

    scale_w = target_width / original_width
    scale_h = target_height / original_height
    scale = min(scale_w, scale_h)
    new_w = int(original_width * scale)
    new_h = int(original_height * scale)
    pad_w = target_width - new_w
    pad_h = target_height - new_h
    top = pad_h // 2
    bottom = pad_h - top
    left = pad_w // 2
    right = pad_w - left

    input_frame_count = -1
    output_frame_count = -1

    out = 
    out = cv2.VideoWriter(output_video_path, fourcc, target_fps, (target_width, target_height))

    while True:
        ret, frame = cap.read()
        if not ret:
            break
        input_frame_count += 1

        if input_frame_count >= start_time * fps:
            out_due = int((input_frame_count - start_time * fps) / fps * target_fps)
            if out_due > output_frame_count:
                resized_frame = cv2.resize(frame, (new_w, new_h), interpolation=cv2.INTER_AREA)
                padded_frame = cv2.copyMakeBorder(resized_frame, top, bottom, left, right, cv2.BORDER_CONSTANT, value=(0, 0, 0))
                out.write(padded_frame)
                output_frame_count += 1
            if input_frame_count >= end_time * fps:
                break

    cap.release()
    out.release()
    logger.info(f"Video resized: {output_video_path}")
    return True

def create_video_by_images(input_images_list, output_video_path, target_width, target_height, target_fps):
    fourcc = cv2.VideoWriter_fourcc(*"avc1")
    out = cv2.VideoWriter(output_video_path, fourcc, target_fps, (target_width, target_height))

    for image in input_images_list:
        img = cv2.imread(image["file_path"])
        original_width, original_height, _ = img.shape
        scale_w = target_width / original_width
        scale_h = target_height / original_height
        scale = min(scale_w, scale_h)
        new_w = int(original_width * scale)
        new_h = int(original_height * scale)
        pad_w = target_width - new_w
        pad_h = target_height - new_h
        top = pad_h // 2
        bottom = pad_h - top
        left = pad_w // 2
        right = pad_w - left
        resized_frame = cv2.resize(img, (new_w, new_h), interpolation=cv2.INTER_AREA)
        padded_frame = cv2.copyMakeBorder(resized_frame, top, bottom, left, right, cv2.BORDER_CONSTANT, value=(0, 0, 0))

        duration = image["end_time"] - image["start_time"]
        frame_count = int(duration * target_fps)
        for _ in range(frame_count):
            out.write(padded_frame)

    out.release()
    logger.info(f"Video created by images: {output_video_path}")
    return True

def convert_video_to_avc1(input_video_path, output_video_path):
    try:
        cap = cv2.VideoCapture(input_video_path)

        if not cap.isOpened():
            logger.error(f"Error: Could not open video file {input_video_path}")
            return False

        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = cap.get(cv2.CAP_PROP_FPS)

        fourcc = cv2.VideoWriter_fourcc(*"avc1")

        out = cv2.VideoWriter(output_video_path, fourcc, fps, (width, height))

        if not out.isOpened():
            logger.error(f"Error: Could not create video writer for {output_video_path}")
            cap.release()
            return False

        while True:
            ret, frame = cap.read()
            if not ret:
                break
            out.write(frame)

        cap.release()
        out.release()

        logger.info(f"Video converted to avc1: {output_video_path}")
        return True

    except Exception as e:
        logger.error(f"Error converting video to avc1: {e}")
        return False

@app.route("/")
def index():
    return send_from_directory("", "index.html")

@app.route("/favicon.ico")
def favicon():
    return send_from_directory("", "favicon.ico", mimetype="image/vnd.microsoft.icon")

@app.route("/css/<path:path>")
def serve_css(path):
    return send_from_directory("css", path)

@app.route("/js/<path:path>")
def serve_js(path):
    return send_from_directory("js", path)

@app.errorhandler(404)
def handle_not_found(error):
    return jsonify({"error": "Not found"}), 404

@app.errorhandler(500)
def handle_internal_error(error):
    logger.error(f"Internal server error: {error}")
    return jsonify({"error": "Internal server error"}), 500

@app.route("/led-fans-storage/<path:filepath>")
def serve_storage_files(filepath):
    try:
        global STORAGE_CONFIG
        storage_dir = os.path.abspath(STORAGE_CONFIG["base_dir"])
        return send_from_directory(storage_dir, filepath)
    except FileNotFoundError:
        logger.error(f"File not found: {filepath}")
        return jsonify({"error": "File not found"}), 404
    except Exception as e:
        logger.error(f"Error serving file {filepath}: {e}")
        return jsonify({"error": "Error serving file"}), 500

@app.route("/api/materials", methods=["POST"])
def upload_file():
    try:
        global STORAGE_CONFIG
        if "file" not in request.files:
            return jsonify({"error": "No file uploaded"}), 400

        file = request.files["file"]
        if file.filename == "":
            return jsonify({"error": "No file selected"}), 400

        file_type = file.mimetype.split("/")[0]
        if file_type not in STORAGE_CONFIG["directories"]["materials"].keys():
            return jsonify({"error": "Invalid file_type"}), 400
            
        sub_dir = STORAGE_CONFIG["directories"]["materials"][file_type]
        upload_path = os.path.join(STORAGE_CONFIG["base_dir"], sub_dir)
        unique_file_name_org = str(STORAGE_CONFIG["indexes"]["materials"][file_type]) + "." + file.filename.split(".")[-1]
        file_path_org = os.path.join(upload_path, unique_file_name_org)
        file.save(file_path_org)
        if file_type == "video":
            unique_file_name_avc1 = str(STORAGE_CONFIG["indexes"]["materials"][file_type]) + ".avc1"
            file_path_avc1 = os.path.join(upload_path, unique_file_name_avc1)
            if not convert_video_to_avc1(file_path_org, file_path_avc1):
                return jsonify({"error": "Convert video to avc1 failed"}), 500
            os.system("rm " + file_path_org)
            logger.info(f"File uploaded: {file_path_avc1}")
        
        logger.info(f"File uploaded: {file_path_org}")

        STORAGE_CONFIG["indexes"]["materials"][file_type] += 1
        with open(os.path.join(STORAGE_CONFIG["base_dir"], STORAGE_CONFIG["directories"]["metadata"], "storage.json"), "w") as f:
            json.dump(STORAGE_CONFIG, f)
        if file_type == "video":
            return jsonify({
                "path": file_path_avc1,
                "size": os.path.getsize(file_path_avc1)
            }), 200
        return jsonify({
            "path": file_path_org,
            "size": os.path.getsize(file_path_org)
        }), 200
    
    except Exception as error:
        logger.error(f"Upload error: {error}")
        return jsonify({"error": "Upload failed"}), 500

@app.route("/api/materials", methods=["DELETE"])
def delete_file():
    try:
        global STORAGE_CONFIG
        request_body = request.get_json(force=True)

        if "file_name" not in request_body.keys():
            return jsonify({"error": "file_name cannot be empty"}), 400
        if "file_type" not in request_body.keys():
            return jsonify({"error": "file_type cannot be empty"}), 400
        if request_body["file_type"] not in STORAGE_CONFIG["directories"]["materials"].keys() and request_body["file_type"] != "medias":
            return jsonify({"error": "Invalid file_type"}), 400
        
        file_path = os.path.join(STORAGE_CONFIG["base_dir"], STORAGE_CONFIG["directories"]["materials"][request_body["file_type"]], request_body["file_name"])

        if not os.path.exists(file_path):
            return jsonify({"error": f"File not found: {file_path}"}), 400
        
        # Remove the physical file
        os.remove(file_path)

        # Clear preview slots that reference the deleted file
        cleared_slots = []
        file_type = request_body["file_type"]
        
        # Only check slots if it's an audio file (music type)
        if file_type == "audio":
            # Check music slots for references to the deleted audio file
            for i, slot in enumerate(STORAGE_CONFIG["preview_slots"]["music"]):
                if slot["music_file_path"] == file_path:
                    STORAGE_CONFIG["preview_slots"]["music"][i]["music_file_path"] = ""
                    cleared_slots.append(f"music slot {i}")
            
            # Check media slots for references to the deleted audio file
            for i, slot in enumerate(STORAGE_CONFIG["preview_slots"]["media"]):
                if slot["music_file_path"] == file_path:
                    STORAGE_CONFIG["preview_slots"]["media"][i]["video_file_path"] = ""
                    STORAGE_CONFIG["preview_slots"]["media"][i]["music_file_path"] = ""
                    cleared_slots.append(f"media slot {i}")
        
        # Save updated configuration if any slots were cleared
        if cleared_slots:
            with open(os.path.join(STORAGE_CONFIG["base_dir"], STORAGE_CONFIG["directories"]["metadata"], "storage.json"), "w") as f:
                json.dump(STORAGE_CONFIG, f)

        logger.info(f"File deleted: {file_path}")
        if cleared_slots:
            logger.info(f"Cleared preview slots: {', '.join(cleared_slots)}")

        return jsonify({
            "path": file_path,
            "cleared_slots": cleared_slots
        }), 200
    
    except Exception as error:
        logger.error(f"delete error: {error}")
        return jsonify({"error": "Delete failed"}), 500

@app.route("/api/materials", methods=["GET"])
def get_file_list():
    try:
        global STORAGE_CONFIG
        file_type = request.args.get("file_type")
        
        if not file_type:
            return jsonify({"error": "file_type query parameter is required"}), 400
        if file_type not in STORAGE_CONFIG["directories"]["materials"].keys():
            return jsonify({"error": "Invalid file_type"}), 400
        
        folder_path = os.path.join(STORAGE_CONFIG["base_dir"], STORAGE_CONFIG["directories"]["materials"][file_type])

        if not os.path.exists(folder_path):
            return jsonify({"error": f"File not found: {folder_path}"}), 400
        
        file_name_list = os.listdir(folder_path)
        file_properties_list = []
        for file_name in file_name_list:
            file_path = os.path.join(folder_path, file_name)
            file_properties = {}
            if file_type == "images":
                file_properties = get_image_properties(file_path)
            elif file_type == "videos" or file_type == "medias":
                file_properties = get_video_properties(file_path)
            elif file_type == "music":
                file_properties = get_audio_properties(file_path)
            if file_properties is None:
                continue
            file_properties["file_name"] = file_name
            file_properties["file_path"] = file_path
            file_properties["file_size"] = os.path.getsize(file_path)
            file_properties_list.append(file_properties)

        logger.info(f"File properties read: {file_name_list}")

        return jsonify({"file_properties_list": file_properties_list}), 200
    
    except Exception as error:
        logger.error(f"get file list error: {error}")
        return jsonify({"error": "get file list failed"}), 500

@app.route("/api/medias/video", methods=["POST"])
def create_medias_by_video():
    try:
        global STORAGE_CONFIG
        global LED_FANS_WIDTH
        global LED_FANS_HEIGHT
        global LED_FANS_FPS
        contain_music = True
        request_body = request.get_json(force=True)

        if "video" not in request_body.keys():
            return jsonify({"error": "video cannot be empty"}), 400
        if "file_path" not in request_body["video"].keys():
            return jsonify({"error": "file_path cannot be empty for video"}), 400
        if "start_time" not in request_body["video"].keys():
            return jsonify({"error": "start_time cannot be empty for video"}), 400
        if "end_time" not in request_body["video"].keys():
            return jsonify({"error": "end_time cannot be empty for video"}), 400
        
        if "music" not in request_body.keys():
            contain_music = False
        elif "file_path" not in request_body["music"].keys():
            contain_music = False
        
        video_file_path = request_body["video"]["file_path"]
        if not os.path.exists(video_file_path):
            return jsonify({"error": f"File not found: {video_file_path}"}), 400
        video_start_time = request_body["video"]["start_time"]
        video_end_time = request_body["video"]["end_time"]
        if 0 > video_end_time - video_start_time > 5:
            return jsonify({"error": "Duratiion of video cannot be negative or greater than 5 seconds"}), 400

        processed_video_file_path = os.path.join(STORAGE_CONFIG["base_dir"], STORAGE_CONFIG["directories"]["medias"], str(STORAGE_CONFIG["indexes"]["medias"]) + ".avc1")
        if not resize_video_by_dimension(video_file_path, processed_video_file_path, LED_FANS_WIDTH, LED_FANS_HEIGHT, video_start_time, video_end_time, LED_FANS_FPS):
            return jsonify({"error": "Resize video failed"}), 500

        if contain_music:
            music_file_path = request_body["music"]["file_path"]
            if not os.path.exists(music_file_path):
                return jsonify({"error": f"File not found: {music_file_path}"}), 400
            processed_music_file_path = os.path.join(STORAGE_CONFIG["base_dir"], STORAGE_CONFIG["directories"]["medias"], str(STORAGE_CONFIG["indexes"]["medias"]) + "." + music_file_path.split(".")[-1])
            os.system(f"cp {music_file_path} {processed_music_file_path}")


        STORAGE_CONFIG["indexes"]["medias"] += 1
        with open(os.path.join(STORAGE_CONFIG["base_dir"], STORAGE_CONFIG["directories"]["metadata"], "storage.json"), "w") as f:
            json.dump(STORAGE_CONFIG, f)

        if contain_music:
            logger.info(f"Media created: {processed_video_file_path}; {processed_music_file_path}")
            
            return jsonify({
                "video_file_path": processed_video_file_path, 
                "music_file_path": processed_music_file_path
            }), 200
        
        logger.info(f"Media created: {processed_video_file_path}")
        return jsonify({
            "video_file_path": processed_video_file_path
        }), 200

    except Exception as error:
        logger.error(f"get file list error: {error}")
        return jsonify({"error": "get file list failed"}), 500
    
@app.route("/api/medias/images", methods=["POST"])
def create_medias_by_images():
    try:
        global STORAGE_CONFIG
        global LED_FANS_WIDTH
        global LED_FANS_HEIGHT
        global LED_FANS_FPS
        contain_music = True
        request_body = request.get_json(force=True)

        if "images" not in request_body.keys():
            return jsonify({"error": "images cannot be empty"}), 400
        if len(request_body["images"]) == 0:
            return jsonify({"error": "At least 1 image is required"}), 400
        if "file_path" not in request_body["images"][0].keys():
            return jsonify({"error": "file_path cannot be empty for images"}), 400
        if "start_time" not in request_body["images"][0].keys():
            return jsonify({"error": "start_time cannot be empty for images"}), 400
        if "end_time" not in request_body["images"][0].keys():
            return jsonify({"error": "end_time cannot be empty for images"}), 400
        
        if "music" not in request_body.keys():
            contain_music = False
        elif "file_path" not in request_body["music"].keys():
            contain_music = False
        
        images_list = request_body["images"]
        total_duration = 0
        for image in images_list:
            image_file_path = image["file_path"]
            if not os.path.exists(image_file_path):
                return jsonify({"error": f"File not found: {image_file_path}"}), 400
            total_duration += (image["end_time"] - image["start_time"])
        if 0 > total_duration > 5:
            return jsonify({"error": "Duratiion of images cannot be negative or greater than 5 seconds"}), 400

        processed_video_file_path = os.path.join(STORAGE_CONFIG["base_dir"], STORAGE_CONFIG["directories"]["medias"], str(STORAGE_CONFIG["indexes"]["medias"]) + ".avc1")
        if not create_video_by_images(images_list, processed_video_file_path, LED_FANS_WIDTH, LED_FANS_HEIGHT, LED_FANS_FPS):
            return jsonify({"error": "Create video by images failed"}), 500


        if contain_music:
            music_file_path = request_body["music"]["file_path"]
            if not os.path.exists(music_file_path):
                return jsonify({"error": f"File not found: {music_file_path}"}), 400
            processed_music_file_path = os.path.join(STORAGE_CONFIG["base_dir"], STORAGE_CONFIG["directories"]["medias"], str(STORAGE_CONFIG["indexes"]["medias"]) + "." + music_file_path.split(".")[-1])
            os.system(f"cp {music_file_path} {processed_music_file_path}")

        STORAGE_CONFIG["indexes"]["medias"] += 1
        with open(os.path.join(STORAGE_CONFIG["base_dir"], STORAGE_CONFIG["directories"]["metadata"], "storage.json"), "w") as f:
            json.dump(STORAGE_CONFIG, f)

        if contain_music:
            logger.info(f"Media created: {processed_video_file_path}; {processed_music_file_path}")

            return jsonify({
                "video_file_path": processed_video_file_path, 
                "music_file_path": processed_music_file_path
            }), 200
        
        logger.info(f"Media created: {processed_video_file_path}")
        return jsonify({
            "video_file_path": processed_video_file_path
        }), 200

    except Exception as error:
        logger.error(f"get file list error: {error}")
        return jsonify({"error": "get file list failed"}), 500

@app.route("/api/medias", methods=["GET"])
def get_media_list():
    try:
        global STORAGE_CONFIG
        media_folder_path = os.path.join(STORAGE_CONFIG["base_dir"], STORAGE_CONFIG["directories"]["medias"])
        media_name_list = os.listdir(media_folder_path)
        media_properties_list = {}
        for media_name in media_name_list:
            media_path = os.path.join(media_folder_path, media_name)
            if "avc1" in media_name:
                media_properties = get_video_properties(media_path)
                media_type = "video"
            else:
                media_properties = get_audio_properties(media_path)
                media_type = "music"
            if media_properties is None:
                continue
            media_properties["media_name"] = media_name
            media_properties["media_path"] = media_path
            media_properties["media_size"] = os.path.getsize(media_path)
            
            base_name = media_name.split(".")[0]
            if base_name not in media_properties_list:
                media_properties_list[base_name] = {}
            media_properties_list[base_name][media_type] = media_properties

        logger.info(f"Media list read: {media_properties_list}")

        return jsonify({"media_properties_list": media_properties_list}), 200
    
    except Exception as error:
        logger.error(f"get media list error: {error}")
        return jsonify({"error": "get media list failed"}), 500

@app.route("/api/medias", methods=["DELETE"])
def delete_media():
    try:
        global STORAGE_CONFIG
        request_body = request.get_json(force=True)
        if "video_file_path" not in request_body.keys():
            return jsonify({"error": "video_file_path cannot be empty"}), 400
        if "music_file_path" not in request_body.keys():
            return jsonify({"error": "music_file_path cannot be empty"}), 400
        
        video_file_path = request_body["video_file_path"]
        if not os.path.exists(video_file_path):
            return jsonify({"error": f"File not found: {video_file_path}"}), 400
        music_file_path = request_body["music_file_path"]
        if not os.path.exists(music_file_path):
            return jsonify({"error": f"File not found: {music_file_path}"}), 400
        
        # Remove the physical files
        os.remove(video_file_path)
        os.remove(music_file_path)

        # Clear preview slots that reference the deleted files
        cleared_slots = []
        
        # Check music slots for references to the deleted music file
        for i, slot in enumerate(STORAGE_CONFIG["preview_slots"]["music"]):
            if slot["music_file_path"] == music_file_path:
                STORAGE_CONFIG["preview_slots"]["music"][i]["music_file_path"] = ""
                cleared_slots.append(f"music slot {i}")
        
        # Check media slots for references to the deleted files
        for i, slot in enumerate(STORAGE_CONFIG["preview_slots"]["media"]):
            if slot["video_file_path"] == video_file_path or slot["music_file_path"] == music_file_path:
                STORAGE_CONFIG["preview_slots"]["media"][i]["video_file_path"] = ""
                STORAGE_CONFIG["preview_slots"]["media"][i]["music_file_path"] = ""
                cleared_slots.append(f"media slot {i}")
        
        # Save updated configuration
        with open(os.path.join(STORAGE_CONFIG["base_dir"], STORAGE_CONFIG["directories"]["metadata"], "storage.json"), "w") as f:
            json.dump(STORAGE_CONFIG, f)

        logger.info(f"Media deleted: {video_file_path}; {music_file_path}")
        if cleared_slots:
            logger.info(f"Cleared preview slots: {', '.join(cleared_slots)}")

        return jsonify({
            "media_name": video_file_path.split(".")[0],
            "video_file_path": video_file_path, 
            "music_file_path": music_file_path,
            "cleared_slots": cleared_slots
        }), 200
    
    except Exception as error:
        logger.error(f"delete media error: {error}")
        return jsonify({"error": "delete media failed"}), 500


@app.route("/api/slots", methods=["POST"])
def update_preview_slot():
    try:
        global STORAGE_CONFIG
        request_body = request.get_json(force=True)

        if "type" not in request_body.keys():
            return jsonify({"error": "type cannot be empty"}), 400
        if "slot_id" not in request_body.keys():
            return jsonify({"error": "slot_id cannot be empty"}), 400
        slot_type = request_body["type"]
        if slot_type not in STORAGE_CONFIG["preview_slots"].keys():
            return jsonify({"error": "Invalid type"}), 400
        if slot_type == "media":
            if "video_file_path" not in request_body.keys():
                return jsonify({"error": "video_file_path cannot be empty"}), 400
        if "music_file_path" not in request_body.keys():
            return jsonify({"error": "music_file_path cannot be empty"}), 400
            
        slot_id = request_body["slot_id"]
        if slot_id < 0 or slot_id >= len(STORAGE_CONFIG["preview_slots"][slot_type]):
            return jsonify({"error": "Invalid slot_id"}), 400
        
        if slot_type == "media":
            video_file_path = request_body["video_file_path"]
            if not os.path.exists(video_file_path):
                return jsonify({"error": f"File not found: {video_file_path}"}), 400
            
        music_file_path = request_body["music_file_path"]
        if not os.path.exists(music_file_path):
            return jsonify({"error": f"File not found: {music_file_path}"}), 400

        if slot_type == "media":
            STORAGE_CONFIG["preview_slots"][slot_type][slot_id]["video_file_path"] = video_file_path

        STORAGE_CONFIG["preview_slots"][slot_type][slot_id]["music_file_path"] = music_file_path

        with open(os.path.join(STORAGE_CONFIG["base_dir"], STORAGE_CONFIG["directories"]["metadata"], "storage.json"), "w") as f:
            json.dump(STORAGE_CONFIG, f)

        return jsonify({"success": True}), 200

    except Exception as error:
        logger.error(f"set preview slot error: {error}")
        return jsonify({"error": "set preview slot failed"}), 500

@app.route("/api/slots", methods=["GET"])
def get_preview_slots():
    try:
        global STORAGE_CONFIG
        slot_type = request.args.get("type")
        
        if not slot_type:
            return jsonify({"error": "type query parameter is required"}), 400
        if slot_type not in STORAGE_CONFIG["preview_slots"].keys():
            return jsonify({"error": "Invalid type"}), 400
        
        return jsonify(STORAGE_CONFIG["preview_slots"][slot_type]), 200
    
    except Exception as error:
        logger.error(f"get preview slots error: {error}")
        return jsonify({"error": "get preview slots failed"}), 500

@app.route("/api/slots", methods=["DELETE"])
def clear_preview_slot():
    try:
        global STORAGE_CONFIG
        request_body = request.get_json(force=True)

        if "type" not in request_body.keys():
            return jsonify({"error": "type cannot be empty"}), 400
        if "slot_id" not in request_body.keys():
            return jsonify({"error": "slot_id cannot be empty"}), 400
        
        slot_type = request_body["type"]
        if slot_type not in STORAGE_CONFIG["preview_slots"].keys():
            return jsonify({"error": "Invalid type"}), 400
        
        slot_id = request_body["slot_id"]
        if slot_id < 0 or slot_id >= len(STORAGE_CONFIG["preview_slots"][slot_type]):
            return jsonify({"error": "Invalid slot_id"}), 400
        
        if slot_type == "media":
            STORAGE_CONFIG["preview_slots"][slot_type][slot_id]["video_file_path"] = ""
            
        STORAGE_CONFIG["preview_slots"][slot_type][slot_id]["music_file_path"] = ""

        with open(os.path.join(STORAGE_CONFIG["base_dir"], STORAGE_CONFIG["directories"]["metadata"], "storage.json"), "w") as f:
            json.dump(STORAGE_CONFIG, f)

        return jsonify({"slot_id": slot_id}), 200
    
    except Exception as error:
        logger.error(f"delete preview slot error: {error}")
        return jsonify({"error": "delete preview slot failed"}), 500

if __name__ == "__main__":
    initialize()
    
    # Start server
    logger.info("Starting Flask server...")
    logger.info(f"Storage directory: {STORAGE_CONFIG['base_dir']}")
    
    # Run on all interfaces (0.0.0.0) like the Node.js version

    # Development Mode
    app.run(host="0.0.0.0", port=3002, debug=False)

    # Production Mode
    # from waitress import serve
    # serve(app, host="0.0.0.0", port=3002)
